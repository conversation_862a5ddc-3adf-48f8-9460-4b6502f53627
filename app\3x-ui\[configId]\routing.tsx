import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Badge } from '@/components/ui/badge';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useAppStore } from '@/lib/store';
import { ThreeXUIConfig } from '@/lib/types';
import { getThreeXUIXrayConfig } from '@/panels/3x-ui/utils';
import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { Plus, Save, RotateCcw, GripVertical } from 'lucide-react-native';
import React, { useState, useEffect, useCallback } from 'react';
import { SafeAreaView, StyleSheet, View, Alert, TouchableOpacity } from 'react-native';
import DragList, { DragListRenderItemInfo } from 'react-native-draglist';

// 路由规则类型定义
interface RouteRule {
  id?: string; // 添加唯一ID用于拖拽
  domainMatcher?: 'hybrid' | 'linear';
  type: 'field';
  domain?: string[];
  ip?: string[];
  port?: string;
  sourcePort?: string;
  network?: 'tcp' | 'udp' | 'tcp,udp';
  source?: string[];
  user?: string[];
  inboundTag?: string[];
  protocol?: ('http' | 'tls' | 'quic' | 'bittorrent')[];
  attrs?: Record<string, string>;
  outboundTag?: string;
  balancerTag?: string;
  ruleTag?: string;
}

export default function RoutingScreen() {
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({}, 'border');

  const { configs, getServerConfig, setServerConfig } = useAppStore();

  const [rules, setRules] = useState<RouteRule[]>([]);
  const [hasChanges, setHasChanges] = useState(false);

  // 获取当前配置
  const currentConfig = configs.find(c => c.id === configId) as ThreeXUIConfig;
  const serverConfig = getServerConfig(configId || '');

  // 加载xray配置
  const loadXrayConfig = useCallback(async () => {
    if (!currentConfig) return;

    try {
      await getThreeXUIXrayConfig(currentConfig);
      // 数据已经在getThreeXUIXrayConfig中存储到serverConfig了，这里不需要额外操作
    } catch (error) {
      console.error('Load xray config failed:', error);
      Alert.alert('错误', '加载路由配置失败');
    }
  }, [currentConfig]);

  // 页面聚焦时加载数据
  useFocusEffect(
    useCallback(() => {
      loadXrayConfig();
    }, [loadXrayConfig])
  );

  // 加载路由规则
  useEffect(() => {
    if (serverConfig?.xray?.routing?.rules) {
      // 为每个规则添加唯一ID（如果没有的话）
      const rulesWithIds = serverConfig.xray.routing.rules.map((rule: RouteRule, index: number) => ({
        ...rule,
        id: rule.id || `rule-${Date.now()}-${index}`
      }));
      setRules(rulesWithIds);
      setHasChanges(false);
    } else {
      setRules([]);
      setHasChanges(false);
    }
  }, [serverConfig]);

  // 添加规则
  const handleAddRule = () => {
    router.push({
      pathname: '/3x-ui/rule-config',
      params: { configId }
    });
  };

  // 编辑规则
  const handleEditRule = (index: number) => {
    router.push({
      pathname: '/3x-ui/rule-config',
      params: { configId, ruleIndex: index.toString() }
    });
  };

  // 删除规则
  const handleDeleteRule = (index: number) => {
    Alert.alert(
      '确认删除',
      '确定要删除这条路由规则吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => {
            const newRules = rules.filter((_, i) => i !== index);
            setRules(newRules);
            setHasChanges(true);
          }
        }
      ]
    );
  };

  // 拖拽重排序
  const handleReorder = async (fromIndex: number, toIndex: number) => {
    const copy = [...rules]; // 不要直接修改 react 数据
    const removed = copy.splice(fromIndex, 1);
    copy.splice(toIndex, 0, removed[0]); // 在新位置插入
    setRules(copy);
    setHasChanges(true);
  };

  // 保存规则
  const handleSave = () => {
    if (!currentConfig) {
      Alert.alert('错误', '未找到配置信息');
      return;
    }

    const updatedServerConfig = { ...serverConfig };
    if (!updatedServerConfig.xray) updatedServerConfig.xray = {};
    if (!updatedServerConfig.xray.routing) updatedServerConfig.xray.routing = {};

    // 移除临时ID，只保存原始规则数据
    const rulesWithoutIds = rules.map(rule => {
      const { id, ...ruleWithoutId } = rule;
      return ruleWithoutId;
    });

    updatedServerConfig.xray.routing.rules = rulesWithoutIds;
    setServerConfig(configId || '', updatedServerConfig);
    setHasChanges(false);

    Alert.alert('成功', '路由规则已保存');
  };

  // 重启服务
  const handleRestart = () => {
    Alert.alert(
      '确认重启',
      '确定要重启Xray服务吗？这将应用新的路由规则配置。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '重启',
          onPress: () => {
            // TODO: 实现重启逻辑
            Alert.alert('提示', '重启功能暂未实现');
          }
        }
      ]
    );
  };

  // 渲染规则项
  const renderRuleItem = ({ item, onDragStart, onDragEnd, isActive, index }: DragListRenderItemInfo<RouteRule>) => {
    const rule = item;

    const getRuleDescription = (rule: RouteRule): string => {
      const parts: string[] = [];

      if (rule.domain && rule.domain.length > 0) {
        parts.push(`域名: ${rule.domain.slice(0, 2).join(', ')}${rule.domain.length > 2 ? '...' : ''}`);
      }
      if (rule.ip && rule.ip.length > 0) {
        parts.push(`IP: ${rule.ip.slice(0, 2).join(', ')}${rule.ip.length > 2 ? '...' : ''}`);
      }
      if (rule.port) {
        parts.push(`端口: ${rule.port}`);
      }
      if (rule.network) {
        parts.push(`网络: ${rule.network}`);
      }
      if (rule.outboundTag) {
        parts.push(`出站: ${rule.outboundTag}`);
      }

      return parts.length > 0 ? parts.join(' | ') : '空规则';
    };

    return (
      <TouchableOpacity
        style={[
          styles.ruleItem,
          { backgroundColor: backgroundColor, borderColor },
          isActive && styles.activeRuleItem
        ]}
        onPress={() => handleEditRule(index)}
        onPressIn={onDragStart}
        onPressOut={onDragEnd}
      >
        <View style={styles.ruleContent}>
          <View style={styles.ruleHeader}>
            <Text style={[styles.ruleTitle, { color: textColor }]}>
              {rule.ruleTag || `规则 ${index + 1}`}
            </Text>
            <View style={styles.ruleActions}>
              <TouchableOpacity
                onPress={() => handleDeleteRule(index)}
                style={styles.deleteButton}
              >
                <Text style={styles.deleteButtonText}>删除</Text>
              </TouchableOpacity>
              <GripVertical size={20} color={textColor + '60'} />
            </View>
          </View>
          <Text style={[styles.ruleDescription, { color: textColor + '80' }]}>
            {getRuleDescription(rule)}
          </Text>
          {rule.outboundTag && (
            <Badge style={styles.outboundBadge}>
              <Text style={styles.badgeText}>{rule.outboundTag}</Text>
            </Badge>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      {/* Header按钮组 */}
      <View style={[styles.header, { borderBottomColor: borderColor }]}>
        <Button
          onPress={handleAddRule}
          size="sm"
          variant="outline"
          style={styles.headerButton}
        >
          <Plus size={16} color={textColor} />
          <Text style={[styles.buttonText, { color: textColor }]}>添加</Text>
        </Button>

        <Button
          onPress={handleSave}
          size="sm"
          disabled={!hasChanges}
          style={[styles.headerButton, !hasChanges && styles.disabledButton]}
        >
          <Save size={16} color={hasChanges ? 'white' : textColor + '60'} />
          <Text style={[styles.buttonText, { color: hasChanges ? 'white' : textColor + '60' }]}>保存</Text>
        </Button>

        <Button
          onPress={handleRestart}
          size="sm"
          variant="outline"
          style={styles.headerButton}
        >
          <RotateCcw size={16} color={textColor} />
          <Text style={[styles.buttonText, { color: textColor }]}>重启</Text>
        </Button>
      </View>



      {/* 规则列表 */}
      <View>
        {rules.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyTitle, { color: textColor }]}>
              暂无路由规则
            </Text>
            <Text style={[styles.emptySubtitle, { color: textColor + '80' }]}>
              点击上方添加按钮创建第一条路由规则
            </Text>
          </View>
        ) : (
          <View>
            <DragList
              data={rules}
              renderItem={renderRuleItem}
              keyExtractor={(item: RouteRule, index: number) => item.id || `rule-${index}`}
              onReordered={handleReorder}
              contentContainerStyle={styles.listContent}
            />
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 4,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  disabledButton: {
    opacity: 0.5,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  listContent: {
    padding: 0,
    height:'100%',
  },
  ruleItem: {
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  activeRuleItem: {
    transform: [{ scale: 1.02 }],
    shadowOpacity: 0.2,
  },
  ruleContent: {
    
  },
  ruleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  ruleTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  ruleActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  deleteButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    backgroundColor: '#ff4444',
  },
  deleteButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  ruleDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  outboundBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    backgroundColor: '#007AFF',
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
});
